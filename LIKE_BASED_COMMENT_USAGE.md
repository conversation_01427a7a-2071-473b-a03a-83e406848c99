# 基于点赞状态的小红书自动评论功能

## 功能更新说明

### 新功能特性

1. **智能重复检测**: 不再基于评论内容检测重复，而是基于点赞状态
2. **自动点赞**: 每个帖子评论前会先自动点赞
3. **点赞历史获取**: 登录后自动获取用户的点赞历史，避免重复操作

### 工作流程

1. **用户登录** → 扫码登录小红书
2. **获取点赞历史** → 自动访问"我-点赞"页面，获取所有已点赞帖子ID
3. **搜索关键词** → 根据指定关键词搜索帖子
4. **智能筛选** → 检查每个帖子是否已点赞过
5. **点赞+评论** → 对未点赞的帖子先点赞再评论
6. **跳过重复** → 已点赞的帖子直接跳过

### 使用方法

#### 基本使用
```bash
python xhs_comment_bot_enhanced.py --keyword "搞笑" --comment "哈哈哈" --count 3
```

#### 使用随机评论
```bash
python xhs_comment_bot_enhanced.py --keyword "搞笑" --random --count 5
```

#### 跳过重复检测（强制评论所有帖子）
```bash
python xhs_comment_bot_enhanced.py --keyword "搞笑" --skip-comment-check --count 3
```

#### 调试模式
```bash
python xhs_comment_bot_enhanced.py --keyword "搞笑" --debug --count 2
```

### 参数说明

- `--keyword, -k`: 搜索关键词
- `--comment, -c`: 评论内容（不指定则使用默认评论）
- `--count, -n`: 要评论的帖子数量
- `--random, -r`: 使用随机评论内容
- `--skip-comment-check`: 跳过重复检测，强制评论所有帖子
- `--debug, -d`: 启用调试模式，输出详细日志
- `--headless`: 无头模式运行（后台运行）

### 测试功能

运行测试脚本验证功能：
```bash
python test_like_based_comment.py
```

### 注意事项

1. **首次运行**: 需要手动扫码登录，后续会自动使用保存的Cookie
2. **网络环境**: 确保网络连接稳定，避免页面加载失败
3. **操作频率**: 内置随机延时，模拟真实用户行为
4. **帖子ID获取**: 依赖小红书的URL格式，如果网站更新可能需要调整
5. **点赞限制**: 小红书可能有点赞频率限制，建议适度使用

### 配置文件

可以在 `config.py` 中调整：
- 延时时间设置
- 默认评论内容
- 浏览器配置
- 页面选择器（如果网站更新）

### 日志文件

- 运行日志保存在 `xhs_bot.log`
- Cookie信息保存在 `xhs_cookies.json`
