#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的基于点赞状态的评论功能
"""

import sys
import os
import time
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from xhs_comment_bot_enhanced import XHSCommentBotEnhanced
from config import CONFIG

def test_updated_functionality():
    """测试更新后的功能"""
    print("=" * 60)
    print("测试更新后的小红书基于点赞状态的自动评论功能")
    print("=" * 60)
    
    # 设置日志级别
    logging.getLogger().setLevel(logging.INFO)
    
    # 创建机器人实例
    bot = XHSCommentBotEnhanced()
    
    # 测试参数
    test_keyword = "搞笑"
    test_comment = "哈哈哈，太有趣了！"
    test_post_count = 1  # 只测试1个帖子
    
    print(f"测试参数:")
    print(f"- 搜索关键词: {test_keyword}")
    print(f"- 评论内容: {test_comment}")
    print(f"- 目标评论数: {test_post_count}")
    print(f"- 重复检测: 基于点赞状态（已更新选择器）")
    print()
    
    try:
        print("开始执行测试...")
        print("请确保已在浏览器中登录小红书账号")
        print()
        
        success = bot.run_comment_task(
            keyword=test_keyword,
            comment_text=test_comment,
            post_count=test_post_count,
            use_random=False
        )
        
        if success:
            print("\n✅ 测试成功完成！")
            print("新功能验证要点:")
            print("1. ✓ 使用实际测试得到的选择器")
            print("2. ✓ 登录后自动获取已点赞帖子列表")
            print("3. ✓ 评论前检查帖子是否已点赞")
            print("4. ✓ 未点赞的帖子先点赞再评论")
            print("5. ✓ 已点赞的帖子直接跳过")
        else:
            print("\n❌ 测试失败！")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出现异常: {e}")
    finally:
        print("\n测试结束")

if __name__ == "__main__":
    test_updated_functionality()
