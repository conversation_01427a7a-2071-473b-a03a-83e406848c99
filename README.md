# 小红书自动评论机器人

基于Playwright实现的小红书自动搜索和评论功能，支持登录状态检测、随机延时、Cookie保存等功能。

## 功能特点

- ✅ **自动登录检测**: 智能检测登录状态，未登录时等待用户扫码
- ✅ **Cookie保存**: 自动保存和加载登录状态，避免重复登录
- ✅ **随机延时**: 模拟人类行为，防止被反爬虫检测
- ✅ **错误处理**: 完善的异常处理和重试机制
- ✅ **日志记录**: 详细的操作日志，便于调试和监控
- ✅ **配置灵活**: 支持配置文件和命令行参数
- ✅ **随机内容**: 支持随机评论内容和搜索关键词

## 安装依赖

```bash
# 安装Python依赖
pip install playwright

# 安装浏览器
playwright install chromium
```

## 使用方法

### 基础版本

```bash
# 运行基础版本（固定评论"哈哈哈"）
python xhs_comment_bot.py
```

### 增强版本

```bash
# 使用默认配置
python xhs_comment_bot_enhanced.py

# 指定搜索关键词和评论内容
python xhs_comment_bot_enhanced.py --keyword "搞笑" --comment "太好笑了" --count 3

# 使用随机评论内容
python xhs_comment_bot_enhanced.py --random

# 无头模式运行（不显示浏览器界面）
python xhs_comment_bot_enhanced.py --headless

# 查看帮助
python xhs_comment_bot_enhanced.py --help
```

## 配置说明

程序配置在 `config.py` 文件中，主要包括：

### 时间配置
- `comment_delay_min/max`: 评论间隔时间范围（3-8秒）
- `page_load_delay`: 页面加载等待时间（2秒）
- `login_check_interval`: 登录状态检查间隔（3秒）
- `max_login_wait_time`: 最大登录等待时间（300秒）

### 浏览器配置
- `headless`: 是否无头模式（默认False，显示浏览器）
- `width/height`: 浏览器窗口大小（1280x720）
- `user_agent`: 用户代理字符串

### 评论内容池
程序内置多种评论内容，使用 `--random` 参数时会随机选择：
- "哈哈哈"
- "太好笑了"
- "笑死我了"
- "有趣"
- "😂😂😂"
- 等等...

## 工作流程

1. **初始化浏览器**: 启动Chromium浏览器
2. **加载Cookie**: 尝试加载之前保存的登录状态
3. **访问首页**: 导航到小红书首页
4. **检查登录**: 检测当前登录状态
5. **等待登录**: 如未登录，等待用户扫码登录
6. **搜索内容**: 搜索指定关键词
7. **评论帖子**: 依次对指定数量的帖子进行评论
8. **随机延时**: 每次评论后随机等待3-8秒
9. **保存状态**: 保存Cookie和日志

## 安全特性

### 反检测机制
- 使用真实浏览器环境，避免被识别为爬虫
- 随机延时模拟人类行为
- 自定义User-Agent
- 禁用自动化检测特征

### 登录安全
- 支持扫码登录，不需要输入密码
- 自动保存和恢复登录状态
- 登录超时保护

### 频率控制
- 评论间隔3-8秒随机延时
- 可配置的时间参数
- 避免高频操作被限制

## 文件说明

- `xhs_comment_bot.py`: 基础版本程序
- `xhs_comment_bot_enhanced.py`: 增强版本程序
- `config.py`: 配置文件
- `xhs_cookies.json`: Cookie保存文件（自动生成）
- `xhs_bot.log`: 运行日志文件（自动生成）
- `requirements.txt`: Python依赖列表

## 注意事项

1. **首次使用**: 需要手动扫码登录一次，之后会自动保存登录状态
2. **频率控制**: 不要过于频繁使用，建议每天使用次数不超过10次
3. **内容合规**: 确保评论内容符合平台规范，避免违规内容
4. **账号安全**: 建议使用小号进行测试，避免主账号被限制
5. **网络环境**: 确保网络连接稳定，避免操作中断

## 故障排除

### 常见问题

1. **登录失败**: 检查网络连接，重新扫码登录
2. **找不到元素**: 页面结构可能发生变化，需要更新选择器
3. **评论失败**: 可能遇到验证码或频率限制，稍后重试
4. **浏览器启动失败**: 确保已安装Chromium浏览器

### 日志分析
程序会生成详细的日志文件 `xhs_bot.log`，包含：
- 操作步骤记录
- 错误信息详情
- 执行时间统计
- 成功/失败统计

## 免责声明

本工具仅供学习和研究使用，请遵守小红书平台的使用条款和相关法律法规。使用者需要对自己的行为负责，开发者不承担任何责任。

## 新增功能

### 随机帖子选择
- ✅ **智能随机选择**: 不再按顺序评论，而是随机选择帖子
- ✅ **重复评论检测**: 自动检测是否已评论过，避免重复评论
- ✅ **替代帖子选择**: 遇到已评论的帖子时，自动选择其他帖子

### 登录优化
- ✅ **智能登录检测**: 更准确的登录状态判断
- ✅ **安全等待机制**: 未登录时不进行页面操作
- ✅ **自动Cookie管理**: 登录状态持久化保存

### 调试功能
- ✅ **调试模式**: `--debug` 参数输出详细信息
- ✅ **跳过检查**: `--skip-comment-check` 跳过重复评论检查
- ✅ **详细日志**: 完整的操作记录和错误信息

## 使用示例

```bash
# 随机选择帖子，使用随机评论内容
python xhs_comment_bot_enhanced.py --random --count 3

# 跳过重复评论检查（测试用）
python xhs_comment_bot_enhanced.py --skip-comment-check --count 2

# 调试模式，查看详细信息
python xhs_comment_bot_enhanced.py --debug --keyword "搞笑" --count 1

# 运行测试脚本
python test_bot.py
```

## 更新日志

### v1.0.0
- 基础自动评论功能
- 登录状态检测
- Cookie保存和加载

### v2.0.0 (Enhanced)
- 配置文件支持
- 随机评论内容
- 命令行参数
- 统计信息显示
- 错误重试机制

### v2.1.0 (Latest)
- 🔥 **随机帖子选择**: 不再按顺序，随机选择帖子评论
- 🔥 **重复评论检测**: 智能检测已评论的帖子，自动跳过
- 🔥 **登录状态优化**: 更准确的登录检测，未登录时安全等待
- 🔥 **调试功能增强**: 新增调试模式和跳过检查选项
- 🔥 **替代帖子机制**: 遇到已评论帖子时自动选择其他帖子
