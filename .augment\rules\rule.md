---
type: "always_apply"
---

1.Always respond in 中文
2.请你在改完代码并提交之后，调用interactive_feedback_mcp反馈工具，等待用户反馈，没问题了再结束对话
3.专注主线任务，遵循最小改动原则，非必要不要修改其他逻辑
4.先一步步思考再生成代码。如果是复杂的需求，可以先制作一个plan，分解任务，写到文件里，再一步一步执行plan
5.如果代码中已实现类似功能，尽量复用现有代码，扩展和修改一下
6.新加的函数可以写一些简洁明了的注释，包括作用、声明变量类型等
7.执行命令（查看/修改文件等）优先使用bash命令行，而不是powershell
8.如果用了powershell，查看或者编辑文件的时候要指定utf8编码，比如：Get-Content -Encoding UTF8
9.virtual-lover这个项目，后端是部署在远程服务器上的，不要在这台电脑上查日志，启动服务
10.创建的临时文件使用完要删除掉，包括测试文件和设计文件。本轮的设计要更新到README.md和DESIGN.md
11.每论对话结束前如果有文件修改，git commit和git push一下。如果没有git仓库请git init一个
12.开发或者修复完一个功能，请写个测试脚本验证一下功能
13.如果是后端应用，启停应用使用启动脚本start.sh。如果启停逻辑有变化也要更新这个脚本