#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小红书自动评论机器人 - 增强版
支持配置文件、随机评论内容、错误重试等功能
"""

import time
import random
import logging
import argparse
from typing import List, Optional
from playwright.sync_api import sync_playwright, Page, Browser
import json
import os
from config import CONFIG, COMMENT_POOL, KEYWORD_POOL

# 配置日志
def setup_logging(log_file: str = None):
    """设置日志配置"""
    log_file = log_file or CONFIG['files']['log_file']
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class XHSCommentBotEnhanced:
    """小红书评论机器人增强版"""
    
    def __init__(self, config: dict = None):
        self.config = config or CONFIG
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.skip_comment_check = False  # 是否跳过重复评论检查
        self.liked_post_ids = set()  # 存储已点赞的帖子ID

        # 统计信息
        self.stats = {
            'total_attempts': 0,
            'successful_comments': 0,
            'failed_comments': 0,
            'skipped_posts': 0,
            'start_time': None,
            'end_time': None,
        }
    
    def setup_browser(self) -> bool:
        """初始化浏览器"""
        try:
            self.playwright = sync_playwright().start()
            browser_config = self.config['browser']
            
            self.browser = self.playwright.chromium.launch(
                headless=browser_config['headless'],
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建新页面
            context = self.browser.new_context(
                viewport={'width': browser_config['width'], 'height': browser_config['height']},
                user_agent=browser_config['user_agent']
            )
            self.page = context.new_page()
            
            logger.info("浏览器初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def save_cookies(self):
        """保存Cookie"""
        try:
            cookies = self.page.context.cookies()
            cookie_file = self.config['files']['cookie_file']
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            logger.info("Cookie保存成功")
        except Exception as e:
            logger.error(f"Cookie保存失败: {e}")
    
    def load_cookies(self) -> bool:
        """加载Cookie"""
        try:
            cookie_file = self.config['files']['cookie_file']
            if os.path.exists(cookie_file):
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                self.page.context.add_cookies(cookies)
                logger.info("Cookie加载成功")
                return True
        except Exception as e:
            logger.error(f"Cookie加载失败: {e}")
        return False
    
    def navigate_to_homepage(self) -> bool:
        """导航到小红书首页"""
        try:
            logger.info("正在访问小红书首页...")
            self.page.goto(self.config['base_url'], wait_until='networkidle')
            time.sleep(self.config['timing']['page_load_delay'])
            logger.info("成功访问小红书首页")
            return True
        except Exception as e:
            logger.error(f"访问首页失败: {e}")
            return False
    
    def check_login_status(self) -> bool:
        """检查登录状态"""
        try:
            # 等待页面加载完成
            time.sleep(1)

            # 使用JavaScript检查登录状态，更准确
            login_status = self.page.evaluate("""
                () => {
                    // 检查是否有登录按钮
                    const loginButtons = document.querySelectorAll('button, a, [class*="login"], [class*="Login"]');
                    let hasLoginButton = false;

                    for (let btn of loginButtons) {
                        const text = btn.textContent?.trim() || '';
                        if (text.includes('登录') || text.includes('登陆') || text.includes('Login')) {
                            hasLoginButton = true;
                            break;
                        }
                    }

                    // 检查是否有用户头像或用户信息
                    const userElements = document.querySelectorAll('.avatar, .user-info, [class*="user"], [class*="User"], [class*="profile"]');
                    const hasUserElements = userElements.length > 0;

                    // 检查URL是否包含登录相关路径
                    const isLoginPage = window.location.href.includes('login') || window.location.href.includes('signin');

                    // 检查是否有搜索框（登录后才会显示）
                    const searchInput = document.querySelector('input[id=search-input], [placeholder*="搜索"]');
                    const hasSearchInput = searchInput !== null;

                    return {
                        hasLoginButton: hasLoginButton,
                        hasUserElements: hasUserElements,
                        isLoginPage: isLoginPage,
                        hasSearchInput: hasSearchInput,
                        isLoggedIn: !hasLoginButton && !isLoginPage && (hasUserElements || hasSearchInput)
                    };
                }
            """)

            logger.info(f"登录状态检测: 登录按钮={login_status['hasLoginButton']}, 用户元素={login_status['hasUserElements']}, 搜索框={login_status['hasSearchInput']}")

            is_logged_in = login_status['isLoggedIn']

            if is_logged_in:
                logger.info("用户已登录")
            else:
                logger.info("用户未登录")

            return is_logged_in

        except Exception as e:
            logger.error(f"检查登录状态失败: {e}")
            return False
    
    def wait_for_login(self) -> bool:
        """等待用户登录"""
        logger.info("检测到未登录状态，请扫码登录...")

        # 尝试点击登录按钮，但不强制要求成功
        try:
            # 使用JavaScript查找并点击登录按钮
            login_clicked = self.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button, a, [class*="login"], [class*="Login"]');
                    for (let btn of buttons) {
                        const text = btn.textContent?.trim() || '';
                        if (text.includes('登录') || text.includes('登陆') || text.includes('Login')) {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }
            """)

            if login_clicked:
                logger.info("已点击登录按钮，请使用小红书APP扫码登录")
                time.sleep(3)  # 等待登录页面加载
            else:
                logger.info("未找到登录按钮，请手动点击登录或刷新页面")

        except Exception as e:
            logger.warning(f"点击登录按钮失败: {e}")

        # 轮询检查登录状态，期间不进行任何页面操作
        timing = self.config['timing']
        start_time = time.time()
        check_count = 0

        while time.time() - start_time < timing['max_login_wait_time']:
            check_count += 1

            # 检查登录状态
            if self.check_login_status():
                logger.info("登录成功！")
                self.save_cookies()
                # 登录成功后等待一下，确保页面完全加载
                time.sleep(2)
                return True

            elapsed_time = int(time.time() - start_time)
            logger.info(f"等待登录中... ({elapsed_time}s) - 第{check_count}次检查")

            # 避免过于频繁的检查
            time.sleep(timing['login_check_interval'])

        logger.error("登录超时，请重试")
        return False
    
    def search_keyword(self, keyword: str) -> bool:
        """搜索关键词"""
        try:
            logger.info(f"正在搜索关键词: {keyword}")
            
            search_selector = self.config['selectors']['search_input']
            search_input = self.page.locator(search_selector)
            if search_input.count() == 0:
                logger.error("未找到搜索框")
                return False
            
            search_input.fill(keyword)
            time.sleep(1)
            search_input.press('Enter')
            
            time.sleep(self.config['timing']['page_load_delay'])
            logger.info("搜索完成")
            return True
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return False
    
    def get_post_count(self) -> int:
        """获取帖子数量"""
        try:
            note_selector = self.config['selectors']['note_item']
            return self.page.locator(note_selector).count()
        except:
            return 0
    
    def click_post(self, index: int) -> bool:
        """点击指定索引的帖子"""
        try:
            note_selector = self.config['selectors']['note_item']
            posts = self.page.locator(note_selector)
            if posts.count() <= index:
                logger.error(f"帖子索引 {index} 超出范围")
                return False

            logger.info(f"正在点击第 {index + 1} 个帖子")
            posts.nth(index).click()
            time.sleep(self.config['timing']['page_load_delay'])
            return True

        except Exception as e:
            logger.error(f"点击帖子失败: {e}")
            return False

    def get_random_post_indices(self, total_posts: int, count: int) -> List[int]:
        """获取随机帖子索引列表"""
        if total_posts <= count:
            return list(range(total_posts))

        # 随机选择不重复的帖子索引
        indices = random.sample(range(total_posts), count)
        logger.info(f"随机选择的帖子索引: {[i+1 for i in indices]}")
        return indices

    def check_own_comments(self) -> bool:
        """检查当前帖子是否已有自己的评论"""
        try:
            # 等待评论区加载
            time.sleep(2)

            # 先检查是否能找到评论输入框，如果找不到说明可能没有评论权限或页面未加载完成
            comment_input_exists = self.page.evaluate(f"""
                () => {{
                    const input = document.querySelector('{self.config['selectors']['comment_input']}');
                    return input !== null;
                }}
            """)

            if not comment_input_exists:
                logger.info("未找到评论输入框，可能无法评论此帖子")
                return True  # 跳过此帖子

            # 使用更精确的方式检查评论
            comment_info = self.page.evaluate("""
                () => {
                    // 查找评论区域，使用更精确的选择器
                    const commentSelectors = [
                        '.comment-list .comment-item',
                        '[class*="comment-item"]',
                        '.comments .comment',
                        '[data-testid*="comment"]'
                    ];

                    let allComments = [];
                    for (let selector of commentSelectors) {
                        const elements = document.querySelectorAll(selector);
                        allComments = allComments.concat(Array.from(elements));
                    }

                    console.log('找到评论元素数量:', allComments.length);

                    // 如果没有找到评论，说明可以评论
                    if (allComments.length === 0) {
                        return { hasOwnComment: false, reason: '没有找到评论' };
                    }

                    // 检查是否有明确的"自己"标识
                    for (let comment of allComments) {
                        const text = comment.textContent || '';

                        // 只检查明确的自己标识，不检查评论内容
                        if (text.includes('自己') || text.includes('作者')) {
                            console.log('发现明确的自己标识:', text.substring(0, 100));
                            return { hasOwnComment: true, reason: '发现自己标识' };
                        }

                        // 检查是否有特殊的CSS类名标识
                        const hasOwnerClass = comment.querySelector('[class*="owner"], [class*="author"], [class*="self"]');
                        if (hasOwnerClass) {
                            console.log('发现owner/author类名');
                            return { hasOwnComment: true, reason: '发现owner类名' };
                        }
                    }

                    return { hasOwnComment: false, reason: '未发现自己的评论' };
                }
            """)

            logger.info(f"评论检测结果: {comment_info['reason']}")

            if comment_info['hasOwnComment']:
                logger.info("检测到已有自己的评论，跳过此帖子")
                return True
            else:
                logger.info("未检测到自己的评论，可以评论")
                return False

        except Exception as e:
            logger.warning(f"检查评论失败: {e}，默认允许评论")
            return False

    def navigate_to_profile(self) -> bool:
        """导航到个人中心页面"""
        try:
            logger.info("正在导航到个人中心...")

            # 尝试多种方式找到个人中心入口
            profile_clicked = self.page.evaluate("""
                () => {
                    // 查找个人中心链接或头像
                    const selectors = [
                        '.user-info', '.avatar', '[class*="user"]', '[class*="User"]',
                        'a[href*="/user/"]', '[class*="profile"]', '.header-user'
                    ];

                    for (let selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        for (let element of elements) {
                            // 检查是否是可点击的个人中心入口
                            if (element.tagName === 'A' || element.onclick || element.style.cursor === 'pointer') {
                                element.click();
                                return true;
                            }
                        }
                    }

                    // 如果没有找到，尝试查找"我"字样的链接
                    const links = document.querySelectorAll('a, button, [role="button"]');
                    for (let link of links) {
                        const text = link.textContent?.trim() || '';
                        if (text === '我' || text.includes('个人中心') || text.includes('Profile')) {
                            link.click();
                            return true;
                        }
                    }

                    return false;
                }
            """)

            if profile_clicked:
                time.sleep(self.config['timing']['page_load_delay'])
                logger.info("成功导航到个人中心")
                return True
            else:
                logger.error("未找到个人中心入口")
                return False

        except Exception as e:
            logger.error(f"导航到个人中心失败: {e}")
            return False

    def navigate_to_liked_posts(self) -> bool:
        """导航到点赞页面"""
        try:
            logger.info("正在导航到点赞页面...")

            # 查找点赞标签页
            liked_clicked = self.page.evaluate("""
                () => {
                    // 查找点赞相关的标签或链接
                    const elements = document.querySelectorAll('a, button, [role="tab"], [role="button"]');
                    for (let element of elements) {
                        const text = element.textContent?.trim() || '';
                        if (text.includes('点赞') || text.includes('喜欢') || text.includes('Like') || text.includes('liked')) {
                            element.click();
                            return true;
                        }
                    }

                    // 尝试查找包含点赞图标的元素
                    const iconElements = document.querySelectorAll('[class*="like"], [class*="heart"]');
                    for (let element of iconElements) {
                        const parent = element.closest('a, button, [role="tab"]');
                        if (parent) {
                            parent.click();
                            return true;
                        }
                    }

                    return false;
                }
            """)

            if liked_clicked:
                time.sleep(self.config['timing']['page_load_delay'])
                logger.info("成功导航到点赞页面")
                return True
            else:
                logger.error("未找到点赞页面入口")
                return False

        except Exception as e:
            logger.error(f"导航到点赞页面失败: {e}")
            return False

    def get_liked_post_ids(self) -> bool:
        """获取已点赞的帖子ID列表"""
        try:
            logger.info("正在获取已点赞的帖子ID列表...")

            # 滚动加载更多点赞的帖子
            scroll_count = 0
            max_scrolls = 5  # 最多滚动5次

            while scroll_count < max_scrolls:
                # 滚动到页面底部
                self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                time.sleep(2)
                scroll_count += 1
                logger.info(f"滚动加载第 {scroll_count} 次")

            # 获取所有已点赞帖子的ID
            post_ids = self.page.evaluate("""
                () => {
                    const postIds = new Set();

                    // 查找所有帖子链接
                    const selectors = [
                        'a[href*="/explore/"]',
                        'a[href*="/discovery/"]',
                        '[class*="note"] a',
                        '[class*="post"] a',
                        '.note-item a'
                    ];

                    for (let selector of selectors) {
                        const links = document.querySelectorAll(selector);
                        for (let link of links) {
                            const href = link.href;
                            if (href && href.includes('/explore/')) {
                                // 从URL中提取帖子ID
                                const match = href.match(/\\/explore\\/([a-zA-Z0-9]+)/);
                                if (match && match[1]) {
                                    postIds.add(match[1]);
                                }
                            }
                        }
                    }

                    return Array.from(postIds);
                }
            """)

            self.liked_post_ids = set(post_ids)
            logger.info(f"获取到 {len(self.liked_post_ids)} 个已点赞的帖子ID")
            logger.debug(f"已点赞帖子ID: {list(self.liked_post_ids)[:10]}...")  # 只显示前10个

            return True

        except Exception as e:
            logger.error(f"获取已点赞帖子ID失败: {e}")
            return False

    def get_post_id(self) -> Optional[str]:
        """获取当前帖子的ID"""
        try:
            # 从当前页面URL中提取帖子ID
            current_url = self.page.url
            logger.debug(f"当前页面URL: {current_url}")

            # 匹配小红书帖子URL格式
            import re
            match = re.search(r'/explore/([a-zA-Z0-9]+)', current_url)
            if match:
                post_id = match.group(1)
                logger.debug(f"提取到帖子ID: {post_id}")
                return post_id
            else:
                logger.warning("无法从URL中提取帖子ID")
                return None

        except Exception as e:
            logger.error(f"获取帖子ID失败: {e}")
            return None

    def like_post(self) -> bool:
        """点赞当前帖子"""
        try:
            logger.info("正在点赞帖子...")

            # 查找并点击点赞按钮
            like_result = self.page.evaluate("""
                () => {
                    // 查找点赞按钮的多种可能选择器
                    const selectors = [
                        '.like-btn:not(.active)',
                        '[class*="like"]:not([class*="active"])',
                        '[class*="Like"]:not([class*="active"])',
                        '.interaction-btn[data-type="like"]',
                        'button[aria-label*="点赞"]',
                        'button[title*="点赞"]'
                    ];

                    for (let selector of selectors) {
                        const likeBtn = document.querySelector(selector);
                        if (likeBtn && !likeBtn.classList.contains('active') && !likeBtn.classList.contains('liked')) {
                            likeBtn.click();
                            return 'liked';
                        }
                    }

                    // 如果没有找到未点赞的按钮，检查是否已经点赞
                    const activeLikeSelectors = [
                        '.like-btn.active',
                        '[class*="like"][class*="active"]',
                        '[class*="liked"]'
                    ];

                    for (let selector of activeLikeSelectors) {
                        if (document.querySelector(selector)) {
                            return 'already_liked';
                        }
                    }

                    return 'no_like_button';
                }
            """)

            if like_result == 'liked':
                logger.info("点赞成功")
                time.sleep(1)  # 等待点赞操作完成
                return True
            elif like_result == 'already_liked':
                logger.info("帖子已经点赞过了")
                return True
            else:
                logger.warning("未找到点赞按钮")
                return False

        except Exception as e:
            logger.error(f"点赞失败: {e}")
            return False
    
    def add_comment(self, comment_text: str) -> bool:
        """添加评论"""
        try:
            logger.info(f"正在添加评论: {comment_text}")
            
            comment_selector = self.config['selectors']['comment_input']
            
            # 使用JavaScript直接操作评论框
            result = self.page.evaluate(f"""
                () => {{
                    const editableElement = document.querySelector('{comment_selector}');
                    if (editableElement) {{
                        editableElement.focus();
                        editableElement.innerHTML = '{comment_text}';
                        editableElement.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        editableElement.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        return 'success';
                    }}
                    return 'no_input_found';
                }}
            """)
            
            if result != 'success':
                logger.error("未找到评论输入框")
                return False
            
            time.sleep(1)
            
            # 点击发送按钮
            send_result = self.page.evaluate("""
                () => {
                    const buttons = document.querySelectorAll('button');
                    for (let button of buttons) {
                        const text = button.textContent.trim();
                        if (text.includes('发送') || text.includes('提交') || text.includes('发布')) {
                            button.click();
                            return 'sent';
                        }
                    }
                    
                    const submitBtn = document.querySelector('button.btn.submit, button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.click();
                        return 'sent';
                    }
                    
                    return 'no_send_button';
                }
            """)
            
            if send_result == 'sent':
                logger.info("评论发送成功")
                self.stats['successful_comments'] += 1
                return True
            else:
                logger.error("未找到发送按钮")
                self.stats['failed_comments'] += 1
                return False
                
        except Exception as e:
            logger.error(f"添加评论失败: {e}")
            self.stats['failed_comments'] += 1
            return False
    
    def random_delay(self):
        """随机延时，模拟人类行为"""
        timing = self.config['timing']
        delay = random.uniform(timing['comment_delay_min'], timing['comment_delay_max'])
        logger.info(f"等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    def go_back(self) -> bool:
        """返回上一页"""
        try:
            self.page.go_back()
            time.sleep(self.config['timing']['page_load_delay'])
            return True
        except Exception as e:
            logger.error(f"返回上一页失败: {e}")
            return False
    
    def get_random_comment(self) -> str:
        """获取随机评论内容"""
        return random.choice(COMMENT_POOL)
    
    def get_random_keyword(self) -> str:
        """获取随机搜索关键词"""
        return random.choice(KEYWORD_POOL)
    
    def run_comment_task(self, keyword: str = None, comment_text: str = None, 
                        post_count: int = None, use_random: bool = False) -> bool:
        """执行评论任务"""
        self.stats['start_time'] = time.time()
        
        try:
            # 使用默认值或随机值
            if not keyword:
                keyword = self.get_random_keyword() if use_random else self.config['task']['default_keyword']
            if not comment_text:
                comment_text = self.get_random_comment() if use_random else self.config['task']['default_comment']
            if not post_count:
                post_count = self.config['task']['default_post_count']
            
            logger.info(f"开始执行任务 - 关键词: {keyword}, 评论: {comment_text}, 帖子数: {post_count}")
            
            # 1. 初始化浏览器
            if not self.setup_browser():
                return False
            
            # 2. 加载Cookie
            self.load_cookies()
            
            # 3. 访问首页
            if not self.navigate_to_homepage():
                return False
            
            # 4. 检查登录状态
            if not self.check_login_status():
                if not self.wait_for_login():
                    return False

            # 5. 获取已点赞的帖子列表
            logger.info("获取已点赞帖子列表...")
            if self.navigate_to_profile():
                if self.navigate_to_liked_posts():
                    self.get_liked_post_ids()
                else:
                    logger.warning("无法访问点赞页面，将跳过重复检测")
            else:
                logger.warning("无法访问个人中心，将跳过重复检测")

            # 返回首页准备搜索
            if not self.navigate_to_homepage():
                logger.warning("返回首页失败，继续执行...")

            # 6. 搜索关键词
            if not self.search_keyword(keyword):
                return False
            
            # 6. 获取帖子数量
            available_posts = self.get_post_count()
            logger.info(f"找到 {available_posts} 个帖子")
            
            if available_posts < post_count:
                logger.warning(f"可用帖子数量不足，将评论 {available_posts} 个帖子")
                post_count = available_posts
            
            # 7. 随机选择帖子进行评论，支持跳过已评论的帖子
            random_indices = self.get_random_post_indices(available_posts, post_count)
            successful_comments = 0
            skipped_posts = []  # 记录跳过的帖子

            for i, post_index in enumerate(random_indices):
                self.stats['total_attempts'] += 1
                logger.info(f"开始处理第 {i + 1}/{post_count} 个帖子 (索引: {post_index + 1})")

                # 点击帖子
                if not self.click_post(post_index):
                    continue

                # 获取当前帖子ID并检查是否已点赞过（如果启用了检查）
                if not self.skip_comment_check:
                    current_post_id = self.get_post_id()
                    if current_post_id and current_post_id in self.liked_post_ids:
                        logger.info(f"帖子 {post_index + 1} (ID: {current_post_id}) 已点赞过，跳过")
                        skipped_posts.append(post_index)
                        self.stats['skipped_posts'] += 1

                        # 返回搜索结果页
                        if not self.go_back():
                            logger.error("返回搜索页面失败")
                            break

                        # 尝试选择一个新的随机帖子（如果还有未尝试的帖子）
                        remaining_posts = [idx for idx in range(available_posts)
                                         if idx not in random_indices and idx not in skipped_posts]

                        if remaining_posts and successful_comments < post_count:
                            new_index = random.choice(remaining_posts)
                            random_indices.append(new_index)
                            logger.info(f"选择新的替代帖子: {new_index + 1}")

                        self.random_delay()
                        continue
                elif self.skip_comment_check:
                    logger.info("跳过重复检查，直接评论")

                # 先点赞帖子
                if not self.like_post():
                    logger.warning(f"帖子 {post_index + 1} 点赞失败，但继续评论")

                # 将当前帖子ID添加到已点赞列表（如果获取到了ID）
                current_post_id = self.get_post_id()
                if current_post_id:
                    self.liked_post_ids.add(current_post_id)

                # 如果使用随机评论，每次都生成新的评论
                current_comment = self.get_random_comment() if use_random else comment_text

                # 添加评论
                if self.add_comment(current_comment):
                    logger.info(f"帖子 {post_index + 1} 评论成功")
                    successful_comments += 1
                else:
                    logger.warning(f"帖子 {post_index + 1} 评论失败")

                # 如果已达到目标评论数，提前结束
                if successful_comments >= post_count:
                    logger.info(f"已完成目标评论数 {post_count}，结束任务")
                    break

                # 返回搜索结果页（除了最后一个）
                if i < len(random_indices) - 1:
                    if not self.go_back():
                        logger.error("返回搜索页面失败")
                        break

                    # 随机延时
                    self.random_delay()

            # 记录跳过的帖子信息
            if skipped_posts:
                logger.info(f"跳过的帖子索引: {[i+1 for i in skipped_posts]} (已有评论)")
            
            self.stats['end_time'] = time.time()
            self.print_stats()
            
            return self.stats['successful_comments'] > 0
            
        except Exception as e:
            logger.error(f"执行任务失败: {e}")
            return False
        finally:
            self.cleanup()
    
    def print_stats(self):
        """打印统计信息"""
        duration = self.stats['end_time'] - self.stats['start_time']
        logger.info("=" * 50)
        logger.info("任务执行统计:")
        logger.info(f"总尝试次数: {self.stats['total_attempts']}")
        logger.info(f"成功评论: {self.stats['successful_comments']}")
        logger.info(f"失败评论: {self.stats['failed_comments']}")
        logger.info(f"跳过帖子: {self.stats['skipped_posts']} (已点赞过)")
        logger.info(f"成功率: {self.stats['successful_comments']/max(self.stats['total_attempts'], 1)*100:.1f}%")
        logger.info(f"执行时间: {duration:.1f} 秒")
        logger.info(f"已点赞帖子总数: {len(self.liked_post_ids)}")
        logger.info("=" * 50)
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='小红书自动评论机器人')
    parser.add_argument('--keyword', '-k', type=str, help='搜索关键词')
    parser.add_argument('--comment', '-c', type=str, help='评论内容')
    parser.add_argument('--count', '-n', type=int, help='评论帖子数量')
    parser.add_argument('--random', '-r', action='store_true', help='使用随机评论内容')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--debug', '-d', action='store_true', help='调试模式，输出更多信息')
    parser.add_argument('--skip-comment-check', action='store_true', help='跳过重复评论检查')

    args = parser.parse_args()

    # 如果指定了无头模式，更新配置
    if args.headless:
        CONFIG['browser']['headless'] = True

    # 如果是调试模式，设置更详细的日志
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("调试模式已启用")

    # 创建机器人实例
    bot = XHSCommentBotEnhanced()
    bot.skip_comment_check = args.skip_comment_check  # 添加跳过检查选项

    # 执行评论任务
    success = bot.run_comment_task(
        keyword=args.keyword,
        comment_text=args.comment,
        post_count=args.count,
        use_random=args.random
    )

    if success:
        print("✅ 任务执行成功！")
    else:
        print("❌ 任务执行失败！")

if __name__ == "__main__":
    main()
